#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مدير النماذج المتعددة الموحد
يدير التبديل بين النماذج المختلفة والنظام القديم
"""

import os
import logging
import asyncio
from typing import Dict, Any, Tuple, Optional
import sys

# إضافة مسار المكتبات المشتركة
current_dir = os.path.dirname(os.path.abspath(__file__))
shared_dir = os.path.dirname(current_dir)
sys.path.insert(0, shared_dir)

from .openrouter_manager import OpenRouterManager
from .usage_limiter import UsageLimiter

# إعداد نظام السجلات
logger = logging.getLogger(__name__)

class MultiModelManager:
    """مدير النماذج المتعددة الموحد"""
    
    def __init__(self):
        """تهيئة مدير النماذج المتعددة"""
        
        # تهيئة المدراء الفرعيين
        self.openrouter = OpenRouterManager()
        self.usage_limiter = UsageLimiter()
        
        # إعدادات النظام
        self.fallback_enabled = True
        self.fallback_system = None  # سيتم تهيئته عند الحاجة
        
        logger.info("🎛️ تم تهيئة مدير النماذج المتعددة الموحد")
    
    def _initialize_fallback_system(self):
        """تهيئة النظام الاحتياطي (DeepSeek القديم)"""
        try:
            # استيراد النظام القديم
            main_dir = os.path.join(os.path.dirname(shared_dir), 'main')
            sys.path.insert(0, main_dir)
            
            from features.exa_ai_normal import ExaAiNormal
            from features.exa_ai_pro import ExaAiPro
            
            self.fallback_system = {
                "normal": ExaAiNormal(),
                "pro": ExaAiPro()
            }
            
            logger.info("🔄 تم تهيئة النظام الاحتياطي (DeepSeek)")
            return True
            
        except Exception as e:
            logger.error(f"خطأ في تهيئة النظام الاحتياطي: {e}")
            return False
    
    async def get_normal_response(self, user_id: int, message: str, conversation_history: list = None) -> Tuple[str, str, bool]:
        """
        الحصول على رد من النماذج العادية
        
        Args:
            user_id: معرف المستخدم
            message: رسالة المستخدم
            conversation_history: تاريخ المحادثة
            
        Returns:
            Tuple[str, str, bool]: (الرد, رسالة الحالة, نجح الطلب)
        """
        try:
            # فحص الحد الأقصى للاستخدام المجاني
            can_use_free, current_usage, limit_message = self.usage_limiter.check_usage_limit(user_id)
            
            if can_use_free:
                # استخدام النماذج المجانية
                try:
                    # اختيار النموذج
                    selected_model = self.openrouter.select_normal_model()
                    
                    # الحصول على الرد مع تحديد نوع النموذج
                    response, input_tokens, output_tokens = await self.openrouter.get_response(
                        selected_model, message, conversation_history, "normal"
                    )
                    
                    # تسجيل الاستخدام
                    self.usage_limiter.record_usage(user_id, "normal")
                    
                    # رسالة الحالة
                    remaining = 25 - (current_usage + 1)
                    status_message = f"ℹ️︙المحاولات المسموح بها︙25︙طلب في الساعة\nℹ️︙المحاولات المتبقية لك︙{remaining}︙طلب في الساعة"
                    
                    logger.info(f"✅ رد مجاني للمستخدم {user_id} - النموذج: {selected_model}")
                    return response, status_message, True
                    
                except Exception as e:
                    logger.error(f"خطأ في النماذج المجانية: {e}")
                    # العودة للنظام الاحتياطي
                    return await self._fallback_normal_response(user_id, message, conversation_history)
            
            else:
                # تجاوز الحد المجاني - استخدام النظام المدفوع
                logger.info(f"💰 تجاوز المستخدم {user_id} الحد المجاني - التحويل للنظام المدفوع")
                
                # هنا يمكن إضافة منطق الفوترة أو العودة للنظام الاحتياطي
                return await self._fallback_normal_response(user_id, message, conversation_history, paid_mode=True)
                
        except Exception as e:
            logger.error(f"خطأ في get_normal_response: {e}")
            return await self._fallback_normal_response(user_id, message, conversation_history)
    
    async def get_pro_response(self, user_id: int, message: str, conversation_history: list = None) -> Tuple[str, int, int]:
        """
        الحصول على رد من النماذج المتقدمة (مدفوع)
        
        Args:
            user_id: معرف المستخدم
            message: رسالة المستخدم
            conversation_history: تاريخ المحادثة
            
        Returns:
            Tuple[str, int, int]: (الرد, input_tokens, output_tokens)
        """
        try:
            # اختيار النموذج المتقدم
            selected_model = self.openrouter.select_pro_model(message)
            
            # الحصول على الرد مع تحديد نوع النموذج المتقدم
            response, input_tokens, output_tokens = await self.openrouter.get_response(
                selected_model, message, conversation_history, "pro"
            )
            
            logger.info(f"✅ رد متقدم للمستخدم {user_id} - النموذج: {selected_model}")
            return response, input_tokens, output_tokens
            
        except Exception as e:
            logger.error(f"خطأ في النماذج المتقدمة: {e}")
            # العودة للنظام الاحتياطي
            return await self._fallback_pro_response(user_id, message, conversation_history)
    
    async def _fallback_normal_response(self, user_id: int, message: str, conversation_history: list = None, paid_mode: bool = False) -> Tuple[str, str, bool]:
        """العودة للنظام الاحتياطي للنماذج العادية"""
        try:
            if not self.fallback_system:
                if not self._initialize_fallback_system():
                    return "عذراً، النظام غير متاح حالياً. يرجى المحاولة لاحقاً.", "خطأ في النظام", False
            
            # استخدام النظام القديم
            response = await self.fallback_system["normal"].get_response(message, conversation_history)
            
            if paid_mode:
                status_message = "نظام احتياطي - تم تجاوز الحد المجاني"
            else:
                status_message = "نظام احتياطي - النماذج الجديدة غير متاحة"
            
            logger.info(f"🔄 استخدام النظام الاحتياطي العادي للمستخدم {user_id}")
            return response, status_message, True
            
        except Exception as e:
            logger.error(f"خطأ في النظام الاحتياطي العادي: {e}")
            return "عذراً، حدث خطأ تقني. يرجى المحاولة مرة أخرى لاحقاً.", "خطأ في النظام الاحتياطي", False
    
    async def _fallback_pro_response(self, user_id: int, message: str, conversation_history: list = None) -> Tuple[str, int, int]:
        """العودة للنظام الاحتياطي للنماذج المتقدمة"""
        try:
            if not self.fallback_system:
                if not self._initialize_fallback_system():
                    return "عذراً، النظام غير متاح حالياً. يرجى المحاولة لاحقاً.", 0, 0
            
            # استخدام النظام القديم
            response, input_tokens, output_tokens = await self.fallback_system["pro"].get_response(message, conversation_history)
            
            logger.info(f"🔄 استخدام النظام الاحتياطي المتقدم للمستخدم {user_id}")
            return response, input_tokens, output_tokens
            
        except Exception as e:
            logger.error(f"خطأ في النظام الاحتياطي المتقدم: {e}")
            return "عذراً، حدث خطأ تقني في الوضع المتقدم. يرجى المحاولة مرة أخرى لاحقاً.", 0, 0
    
    def get_user_usage_info(self, user_id: int) -> Dict[str, Any]:
        """الحصول على معلومات استخدام المستخدم"""
        try:
            usage_stats = self.usage_limiter.get_user_usage_stats(user_id)
            
            return {
                "user_id": user_id,
                "free_usage": usage_stats,
                "can_use_free": usage_stats.get("can_use_free", True),
                "models_available": {
                    "normal_free": usage_stats.get("can_use_free", True),
                    "normal_paid": True,  # دائماً متاح (مع الفوترة)
                    "pro_paid": True      # دائماً متاح (مع الفوترة)
                }
            }
            
        except Exception as e:
            logger.error(f"خطأ في الحصول على معلومات المستخدم {user_id}: {e}")
            return {"user_id": user_id, "error": str(e)}
    
    def get_system_status(self) -> Dict[str, Any]:
        """الحصول على حالة النظام"""
        try:
            # إحصائيات OpenRouter
            openrouter_stats = self.openrouter.get_performance_summary()
            
            # إحصائيات الاستخدام
            usage_stats = self.usage_limiter.get_overall_usage_stats()
            
            # حالة النظام الاحتياطي
            fallback_status = "متاح" if self.fallback_system else "غير مهيأ"
            
            return {
                "openrouter_models": openrouter_stats,
                "usage_limits": usage_stats,
                "fallback_system": fallback_status,
                "system_health": "جيد",  # يمكن تحسينه لاحقاً
                "last_updated": self.openrouter.performance_stats
            }
            
        except Exception as e:
            logger.error(f"خطأ في الحصول على حالة النظام: {e}")
            return {"error": str(e)}
    
    def reset_user_limits(self, user_id: int) -> bool:
        """إعادة تعيين حدود المستخدم (للطوارئ)"""
        try:
            return self.usage_limiter.reset_user_usage(user_id)
        except Exception as e:
            logger.error(f"خطأ في إعادة تعيين حدود المستخدم {user_id}: {e}")
            return False
    
    async def test_all_models(self, test_message: str = "مرحبا") -> Dict[str, Any]:
        """اختبار جميع النماذج"""
        results = {
            "normal_models": {},
            "pro_models": {},
            "fallback_system": {}
        }
        
        try:
            # اختبار النماذج العادية
            for model_id in self.openrouter.models["normal"]:
                try:
                    response, input_tokens, output_tokens = await self.openrouter.get_response(
                        model_id, test_message, None, "normal"
                    )
                    results["normal_models"][model_id] = {
                        "status": "success",
                        "response_length": len(response),
                        "tokens": {"input": input_tokens, "output": output_tokens}
                    }
                except Exception as e:
                    results["normal_models"][model_id] = {
                        "status": "failed",
                        "error": str(e)
                    }
            
            # اختبار النماذج المتقدمة
            for model_id in self.openrouter.models["pro"]:
                try:
                    response, input_tokens, output_tokens = await self.openrouter.get_response(
                        model_id, test_message, None, "pro"
                    )
                    results["pro_models"][model_id] = {
                        "status": "success",
                        "response_length": len(response),
                        "tokens": {"input": input_tokens, "output": output_tokens}
                    }
                except Exception as e:
                    results["pro_models"][model_id] = {
                        "status": "failed",
                        "error": str(e)
                    }
            
            # اختبار النظام الاحتياطي
            try:
                if not self.fallback_system:
                    self._initialize_fallback_system()
                
                if self.fallback_system:
                    normal_response = await self.fallback_system["normal"].get_response(test_message)
                    pro_response, input_tokens, output_tokens = await self.fallback_system["pro"].get_response(test_message)
                    
                    results["fallback_system"] = {
                        "normal": {"status": "success", "response_length": len(normal_response)},
                        "pro": {"status": "success", "response_length": len(pro_response), "tokens": {"input": input_tokens, "output": output_tokens}}
                    }
                else:
                    results["fallback_system"] = {"status": "failed", "error": "لا يمكن تهيئة النظام الاحتياطي"}
                    
            except Exception as e:
                results["fallback_system"] = {"status": "failed", "error": str(e)}
            
            return results
            
        except Exception as e:
            logger.error(f"خطأ في اختبار النماذج: {e}")
            return {"error": str(e)}
