#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مدير النماذج الذكية للبوت الإداري
يوفر واجهة تحكم شاملة لجميع النماذج الذكية في النظام
"""

import json
import os
import sys
import logging
from datetime import datetime
from typing import Dict, Any, List, Tuple
from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup, ReplyKeyboardMarkup
from telegram.ext import ContextTypes

# إضافة المجلد الجذر لمسار Python
root_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, root_dir)

logger = logging.getLogger(__name__)

class AIModelsManager:
    """مدير النماذج الذكية"""
    
    def __init__(self):
        """تهيئة مدير النماذج الذكية"""
        self.models_config_file = os.path.join(root_dir, "shared", "database", "ai_models_config.json")
        self.models_config = self.load_models_config()
        
        # تعريف النماذج المتاحة
        self.available_models = {
            "exa_normal": {
                "name": "إكسا الذكي العادي",
                "description": "للمحادثات العامة والأسئلة البسيطة",
                "provider": "DeepSeek Chat",
                "model_id": "deepseek-chat",
                "type": "primary",
                "icon": "🤖"
            },
            "exa_pro": {
                "name": "إكسا الذكي برو",
                "description": "للاستشارات المتقدمة والمعقدة",
                "provider": "DeepSeek-R1",
                "model_id": "deepseek-reasoner",
                "type": "primary",
                "icon": "🧠"
            },
            "gemma_27b": {
                "name": "Gemma 2 27B (OR)",
                "description": "نموذج Google السريع والفعال",
                "provider": "OpenRouter",
                "model_id": "google/gemma-2-27b-it",
                "type": "secondary",
                "icon": "🔷"
            },
            "gemma_9b": {
                "name": "Gemma 2 9B Free (OR)",
                "description": "نموذج Google السريع والمجاني",
                "provider": "OpenRouter",
                "model_id": "google/gemma-2-9b-it:free",
                "type": "secondary",
                "icon": "🔹"
            },
            "deepseek_r1": {
                "name": "DeepSeek R1 (OR)",
                "description": "نموذج التفكير المتقدم من DeepSeek",
                "provider": "OpenRouter",
                "model_id": "deepseek/deepseek-r1",
                "type": "secondary",
                "icon": "🧩"
            },
            "gpt_35": {
                "name": "GPT-3.5 Turbo (OR)",
                "description": "نموذج OpenAI للتحليل المتقدم",
                "provider": "OpenRouter",
                "model_id": "openai/gpt-3.5-turbo",
                "type": "secondary",
                "icon": "⚡"
            }
        }
        
        logger.info("🤖 تم تهيئة مدير النماذج الذكية")
    
    def load_models_config(self) -> Dict[str, Any]:
        """تحميل إعدادات النماذج"""
        try:
            if os.path.exists(self.models_config_file):
                with open(self.models_config_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            else:
                # إنشاء إعدادات افتراضية
                default_config = {}
                for model_key in ["exa_normal", "exa_pro", "gemma_27b", "gemma_9b", "deepseek_r1", "gpt_35"]:
                    default_config[model_key] = {
                        "status": "active",
                        "priority": 1,
                        "max_tokens": 1000,
                        "temperature": 0.7,
                        "timeout": 30,
                        "fallback_enabled": True,
                        "last_updated": datetime.now().isoformat()
                    }
                
                self.save_models_config(default_config)
                return default_config
                
        except Exception as e:
            logger.error(f"خطأ في تحميل إعدادات النماذج: {e}")
            return {}
    
    def save_models_config(self, config: Dict[str, Any]):
        """حفظ إعدادات النماذج"""
        try:
            os.makedirs(os.path.dirname(self.models_config_file), exist_ok=True)
            with open(self.models_config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)
            logger.info("✅ تم حفظ إعدادات النماذج")
        except Exception as e:
            logger.error(f"خطأ في حفظ إعدادات النماذج: {e}")
    
    def get_models_keyboard(self):
        """إنشاء لوحة مفاتيح الأزرار السفلية للنماذج"""
        keyboard = []

        # فاصل للنماذج الرئيسية
        keyboard.append(["━━━ النماذج الرئيسية (DeepSeek) ━━━"])

        # جمع النماذج الرئيسية
        primary_buttons = []
        for model_key, model_info in self.available_models.items():
            if model_info['type'] == 'primary':
                status_icon = "🟢" if self.models_config.get(model_key, {}).get("status") == "active" else "🔴"
                # إزالة كلمة "الذكي" لتوفير المساحة
                display_name = model_info['name'].replace("إكسا الذكي ", "إكسا ")
                button_text = f"{model_info['icon']} {display_name} {status_icon}"
                primary_buttons.append(button_text)

        # ترتيب النماذج الرئيسية: DeepSeek Chat بجانب DeepSeek-R1
        if len(primary_buttons) >= 2:
            keyboard.append([primary_buttons[0], primary_buttons[1]])  # إكسا العادي وإكسا برو

        # فاصل للنماذج الفرعية
        keyboard.append(["━━━ النماذج الفرعية (OpenRouter) ━━━"])

        # جمع النماذج الفرعية
        secondary_buttons = []
        for model_key, model_info in self.available_models.items():
            if model_info['type'] == 'secondary':
                status_icon = "🟢" if self.models_config.get(model_key, {}).get("status") == "active" else "🔴"
                button_text = f"{model_info['icon']} {model_info['name']} {status_icon}"
                secondary_buttons.append(button_text)

        # ترتيب النماذج الفرعية: كل زرين جنب بعض
        for i in range(0, len(secondary_buttons), 2):
            if i + 1 < len(secondary_buttons):
                keyboard.append([secondary_buttons[i], secondary_buttons[i + 1]])
            else:
                keyboard.append([secondary_buttons[i]])  # الزر الأخير لوحده إذا كان العدد فردي

        # أزرار إضافية
        keyboard.append(["📊 إحصائيات النماذج", "⚙️ إعدادات عامة"])
        keyboard.append(["🔙 العودة للقائمة الرئيسية"])

        return ReplyKeyboardMarkup(keyboard, resize_keyboard=True, one_time_keyboard=False)

    async def show_models_menu(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """عرض قائمة النماذج مع الأزرار السفلية"""
        try:
            # تعيين حالة المستخدم لإدارة النماذج
            user_id = update.effective_user.id
            if not hasattr(context, 'user_data'):
                context.user_data = {}
            context.user_data['current_menu'] = 'ai_models'

            message_text = """🤖 **إدارة النماذج الذكية**

🎯 **النماذج الرئيسية (DeepSeek):**
• إكسا العادي: للمحادثات البسيطة (DeepSeek Chat)
• إكسا برو: للاستشارات المعقدة (DeepSeek-R1)

🔧 **النماذج الفرعية (OpenRouter):**
• نماذج متنوعة للدعم والاحتياط
• Gemma، DeepSeek R1، GPT-3.5 Turbo

📊 **الحالة:**
🟢 = نشط | 🔴 = متوقف

استخدم الأزرار أدناه لإدارة النماذج:"""

            # الحصول على لوحة المفاتيح المخصصة للنماذج
            reply_markup = self.get_models_keyboard()

            if update.callback_query:
                await update.callback_query.edit_message_text(
                    text=message_text,
                    parse_mode='Markdown'
                )
                # إرسال رسالة جديدة مع الأزرار السفلية
                await update.callback_query.message.reply_text(
                    "اختر النموذج المطلوب إدارته:",
                    reply_markup=reply_markup
                )
            else:
                await update.message.reply_text(
                    text=message_text,
                    reply_markup=reply_markup,
                    parse_mode='Markdown'
                )

        except Exception as e:
            logger.error(f"خطأ في عرض قائمة النماذج: {e}")
            await update.message.reply_text("❌ خطأ في عرض قائمة النماذج")

    def find_model_by_button_text(self, button_text: str) -> str:
        """العثور على مفتاح النموذج من نص الزر"""
        # إزالة الأيقونات والحالة من النص
        clean_text = button_text

        # إزالة الأيقونات والحالة
        for icon in ["🤖", "🧠", "🔷", "🔹", "🧩", "⚡", "🟢", "🔴"]:
            clean_text = clean_text.replace(icon, "").strip()

        # البحث عن النموذج المطابق
        for model_key, model_info in self.available_models.items():
            # البحث بالاسم الكامل
            if model_info['name'] in clean_text:
                return model_key
            # البحث بالاسم المختصر للنماذج الرئيسية
            if model_info['type'] == 'primary':
                short_name = model_info['name'].replace("إكسا الذكي ", "إكسا ")
                if short_name in clean_text:
                    return model_key

        return None

    async def handle_model_button(self, update: Update, context: ContextTypes.DEFAULT_TYPE, button_text: str):
        """معالجة الضغط على زر نموذج من الأزرار السفلية"""
        try:
            # تجاهل الفواصل
            if "━━━" in button_text:
                if "النماذج الرئيسية" in button_text:
                    await update.message.reply_text("ℹ️ هذا فاصل للنماذج الرئيسية (DeepSeek)")
                elif "النماذج الفرعية" in button_text:
                    await update.message.reply_text("ℹ️ هذا فاصل للنماذج الفرعية (OpenRouter)")
                else:
                    await update.message.reply_text("ℹ️ هذا مجرد فاصل للتنظيم")
                return

            # معالجة الأزرار الخاصة
            if button_text == "📊 إحصائيات النماذج":
                await self.show_models_statistics_bottom(update, context)
                return
            elif button_text == "⚙️ إعدادات عامة":
                await self.show_general_settings_bottom(update, context)
                return
            elif button_text == "🔙 العودة للقائمة الرئيسية":
                # العودة للأزرار الرئيسية
                from admin.admin import UnifiedAdminBot
                admin_bot = UnifiedAdminBot()
                await update.message.reply_text(
                    "🔙 تم العودة للقائمة الرئيسية",
                    reply_markup=admin_bot.get_reply_keyboard()
                )
                context.user_data['current_menu'] = 'main'
                return

            # العثور على النموذج
            model_key = self.find_model_by_button_text(button_text)
            if model_key:
                await self.show_model_details_bottom(update, context, model_key)
            else:
                await update.message.reply_text("❌ لم يتم العثور على النموذج")

        except Exception as e:
            logger.error(f"خطأ في معالجة زر النموذج: {e}")
            await update.message.reply_text("❌ خطأ في معالجة الطلب")

    def get_model_control_keyboard(self, model_key: str):
        """إنشاء لوحة مفاتيح التحكم في النموذج"""
        model_config = self.models_config.get(model_key, {})
        status = model_config.get("status", "active")

        keyboard = []

        # زر تبديل الحالة
        if status == "active":
            keyboard.append(["🔴 إيقاف النموذج"])
        else:
            keyboard.append(["🟢 تشغيل النموذج"])

        # أزرار التحكم الإضافية
        keyboard.append(["🔄 إعادة تشغيل", "⚙️ الإعدادات"])
        keyboard.append(["📊 الإحصائيات", "🧪 اختبار النموذج"])
        keyboard.append(["🔙 العودة للنماذج"])

        return ReplyKeyboardMarkup(keyboard, resize_keyboard=True, one_time_keyboard=False)

    async def show_model_details_bottom(self, update: Update, context: ContextTypes.DEFAULT_TYPE, model_key: str):
        """عرض تفاصيل النموذج مع الأزرار السفلية"""
        try:
            if model_key not in self.available_models:
                await update.message.reply_text("❌ نموذج غير موجود")
                return

            model_info = self.available_models[model_key]
            model_config = self.models_config.get(model_key, {})

            # حالة النموذج
            status = model_config.get("status", "active")
            status_text = "🟢 نشط ومتاح للاستخدام" if status == "active" else "🔴 متوقف وغير متاح"
            status_icon = "🟢" if status == "active" else "🔴"

            # تحديد نوع النموذج
            model_type_text = "رئيسي" if model_info['type'] == 'primary' else "فرعي (OpenRouter)"

            # تعيين حالة المستخدم
            context.user_data['current_menu'] = 'model_details'
            context.user_data['current_model'] = model_key

            message_text = f"""🤖 **{model_info['name']}**

{status_icon} **الحالة الحالية:** {status_text}

📋 **معلومات النموذج:**
🏢 **المزود:** {model_info['provider']}
🏷️ **النوع:** {model_type_text}
🆔 **معرف النموذج:** `{model_info['model_id']}`
📝 **الوصف:** {model_info['description']}

⚙️ **الإعدادات النشطة:**
🎯 **الأولوية:** {model_config.get('priority', 1)} (كلما قل الرقم، زادت الأولوية)
🎫 **أقصى توكنز:** {model_config.get('max_tokens', 1000)} توكن
🌡️ **درجة الحرارة:** {model_config.get('temperature', 0.7)} (الإبداعية)
⏰ **المهلة الزمنية:** {model_config.get('timeout', 30)} ثانية
🛡️ **النظام الاحتياطي:** {'✅ مفعل' if model_config.get('fallback_enabled', True) else '❌ معطل'}

📅 **آخر تحديث:** {model_config.get('last_updated', 'غير محدد')[:16]}

💡 **استخدم الأزرار أدناه للتحكم في النموذج:**"""

            # الحصول على لوحة المفاتيح المخصصة للنموذج
            reply_markup = self.get_model_control_keyboard(model_key)

            await update.message.reply_text(
                text=message_text,
                reply_markup=reply_markup,
                parse_mode='Markdown'
            )

        except Exception as e:
            logger.error(f"خطأ في عرض تفاصيل النموذج: {e}")
            await update.message.reply_text("❌ خطأ في عرض تفاصيل النموذج")

    async def handle_model_control_button(self, update: Update, context: ContextTypes.DEFAULT_TYPE, button_text: str):
        """معالجة أزرار التحكم في النموذج"""
        try:
            model_key = context.user_data.get('current_model')
            if not model_key:
                await update.message.reply_text("❌ لم يتم تحديد النموذج")
                return

            model_info = self.available_models.get(model_key)
            if not model_info:
                await update.message.reply_text("❌ نموذج غير موجود")
                return

            if button_text == "🟢 تشغيل النموذج":
                await self.toggle_model_status_bottom(update, context, model_key, "active")
            elif button_text == "🔴 إيقاف النموذج":
                await self.toggle_model_status_bottom(update, context, model_key, "inactive")
            elif button_text == "🔄 إعادة تشغيل":
                await self.restart_model_bottom(update, context, model_key)
            elif button_text == "⚙️ الإعدادات":
                await self.show_model_settings_bottom(update, context, model_key)
            elif button_text == "📊 الإحصائيات":
                await self.show_model_stats_bottom(update, context, model_key)
            elif button_text == "🧪 اختبار النموذج":
                await self.test_model_bottom(update, context, model_key)
            elif button_text == "🔙 العودة للنماذج":
                context.user_data['current_menu'] = 'ai_models'
                context.user_data.pop('current_model', None)
                await self.show_models_menu(update, context)
            else:
                await update.message.reply_text("❌ أمر غير معروف")

        except Exception as e:
            logger.error(f"خطأ في معالجة زر التحكم: {e}")
            await update.message.reply_text("❌ خطأ في معالجة الطلب")

    async def toggle_model_status_bottom(self, update: Update, context: ContextTypes.DEFAULT_TYPE, model_key: str, new_status: str):
        """تبديل حالة النموذج مع الأزرار السفلية"""
        try:
            model_info = self.available_models[model_key]

            # تحديث الإعدادات
            if model_key not in self.models_config:
                self.models_config[model_key] = {}

            self.models_config[model_key]["status"] = new_status
            self.models_config[model_key]["last_updated"] = datetime.now().isoformat()

            # حفظ التغييرات
            self.save_models_config(self.models_config)

            # رسالة التأكيد
            if new_status == "active":
                confirmation_msg = f"✅ تم تشغيل {model_info['name']} بنجاح!\n🟢 النموذج نشط الآن ومتاح للاستخدام في البوت"
            else:
                confirmation_msg = f"⏸️ تم إيقاف {model_info['name']} بنجاح!\n🔴 النموذج متوقف الآن وغير متاح للاستخدام"

            await update.message.reply_text(confirmation_msg)

            # إعادة عرض تفاصيل النموذج مع الحالة الجديدة
            await self.show_model_details_bottom(update, context, model_key)

            logger.info(f"🔄 تم تغيير حالة النموذج {model_key} إلى {new_status}")

        except Exception as e:
            logger.error(f"خطأ في تبديل حالة النموذج: {e}")
            await update.message.reply_text("❌ خطأ في تغيير حالة النموذج")

    async def restart_model_bottom(self, update: Update, context: ContextTypes.DEFAULT_TYPE, model_key: str):
        """إعادة تشغيل النموذج"""
        try:
            model_info = self.available_models[model_key]

            # تحديث الإعدادات
            if model_key not in self.models_config:
                self.models_config[model_key] = {}

            self.models_config[model_key]["status"] = "active"
            self.models_config[model_key]["last_updated"] = datetime.now().isoformat()

            # حفظ التغييرات
            self.save_models_config(self.models_config)

            confirmation_msg = f"🔄 تم إعادة تشغيل {model_info['name']} بنجاح!\n🟢 النموذج نشط الآن ومتاح للاستخدام"

            await update.message.reply_text(confirmation_msg)

            # إعادة عرض تفاصيل النموذج
            await self.show_model_details_bottom(update, context, model_key)

            logger.info(f"🔄 تم إعادة تشغيل النموذج {model_key}")

        except Exception as e:
            logger.error(f"خطأ في إعادة تشغيل النموذج: {e}")
            await update.message.reply_text("❌ خطأ في إعادة تشغيل النموذج")

    async def show_models_statistics_bottom(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """عرض إحصائيات النماذج مع الأزرار السفلية"""
        try:
            # جمع الإحصائيات
            active_models = 0
            inactive_models = 0
            total_models = len(self.available_models)

            primary_active = 0
            secondary_active = 0

            for model_key, model_info in self.available_models.items():
                model_config = self.models_config.get(model_key, {})

                if model_config.get("status", "active") == "active":
                    active_models += 1
                    if model_info['type'] == 'primary':
                        primary_active += 1
                    else:
                        secondary_active += 1
                else:
                    inactive_models += 1

            message_text = f"""📊 **إحصائيات النماذج الذكية**

🎯 **الإحصائيات العامة:**
• إجمالي النماذج: {total_models}
• النماذج النشطة: {active_models} 🟢
• النماذج المتوقفة: {inactive_models} 🔴

📈 **التوزيع حسب النوع:**
• النماذج الرئيسية النشطة: {primary_active}/2
• النماذج الفرعية النشطة: {secondary_active}/4

🏢 **المزودين:**
• DeepSeek Chat: 1 نموذج
• DeepSeek-R1: 1 نموذج
• OpenRouter: 4 نماذج

📅 **آخر تحديث:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"""

            await update.message.reply_text(
                text=message_text,
                parse_mode='Markdown'
            )

        except Exception as e:
            logger.error(f"خطأ في عرض إحصائيات النماذج: {e}")
            await update.message.reply_text("❌ خطأ في عرض الإحصائيات")

    async def show_general_settings_bottom(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """عرض الإعدادات العامة"""
        try:
            message_text = """⚙️ **الإعدادات العامة للنماذج**

🔧 **الإعدادات المتاحة:**
• تشغيل/إيقاف النماذج
• تعديل الأولوية
• ضبط التوكنز وdrجة الحرارة
• إدارة المهلة الزمنية
• تفعيل/تعطيل النظام الاحتياطي

💡 **نصائح:**
• النماذج الرئيسية لها أولوية أعلى
• النماذج الفرعية تعمل كاحتياطي
• يمكن تشغيل عدة نماذج معاً

📝 **للوصول للإعدادات:**
اختر النموذج المطلوب ثم اضغط "⚙️ الإعدادات" """

            await update.message.reply_text(
                text=message_text,
                parse_mode='Markdown'
            )

        except Exception as e:
            logger.error(f"خطأ في عرض الإعدادات العامة: {e}")
            await update.message.reply_text("❌ خطأ في عرض الإعدادات")

    async def show_model_settings_bottom(self, update: Update, context: ContextTypes.DEFAULT_TYPE, model_key: str):
        """عرض إعدادات النموذج المحدد"""
        try:
            model_info = self.available_models[model_key]
            model_config = self.models_config.get(model_key, {})

            message_text = f"""⚙️ **إعدادات {model_info['name']}**

🎯 **الإعدادات الحالية:**

📊 **الأولوية:** {model_config.get('priority', 1)}
• كلما قل الرقم، زادت الأولوية في الاختيار

🎫 **أقصى توكنز:** {model_config.get('max_tokens', 1000)}
• عدد الكلمات/الرموز المسموح بها في الرد

🌡️ **درجة الحرارة:** {model_config.get('temperature', 0.7)}
• تحكم في إبداعية الردود (0.1 = دقيق، 1.0 = إبداعي)

⏰ **المهلة الزمنية:** {model_config.get('timeout', 30)} ثانية
• الوقت الأقصى لانتظار الرد

🛡️ **النظام الاحتياطي:** {'مفعل ✅' if model_config.get('fallback_enabled', True) else 'معطل ❌'}
• التحويل لنموذج آخر عند الفشل

💡 **ملاحظة:** لتعديل هذه الإعدادات، يمكنك استخدام الأزرار المضمنة في الإصدارات المستقبلية."""

            await update.message.reply_text(
                text=message_text,
                parse_mode='Markdown'
            )

        except Exception as e:
            logger.error(f"خطأ في عرض إعدادات النموذج: {e}")
            await update.message.reply_text("❌ خطأ في عرض الإعدادات")

    async def show_model_stats_bottom(self, update: Update, context: ContextTypes.DEFAULT_TYPE, model_key: str):
        """عرض إحصائيات النموذج المحدد"""
        try:
            model_info = self.available_models[model_key]
            model_config = self.models_config.get(model_key, {})

            status = model_config.get("status", "active")
            status_text = "🟢 نشط" if status == "active" else "🔴 متوقف"

            message_text = f"""📊 **إحصائيات {model_info['name']}**

📋 **معلومات النموذج:**
• الحالة: {status_text}
• المزود: {model_info['provider']}
• النوع: {'رئيسي' if model_info['type'] == 'primary' else 'فرعي (OpenRouter)'}

⚙️ **الإعدادات النشطة:**
• الأولوية: {model_config.get('priority', 1)}
• أقصى توكنز: {model_config.get('max_tokens', 1000)}
• درجة الحرارة: {model_config.get('temperature', 0.7)}
• المهلة الزمنية: {model_config.get('timeout', 30)} ثانية

📅 **معلومات التحديث:**
• آخر تحديث: {model_config.get('last_updated', 'غير محدد')[:16]}

📈 **الأداء:** (محاكاة)
• الطلبات الناجحة: 95%
• متوسط وقت الاستجابة: 2.3 ثانية
• معدل الاستخدام: متوسط"""

            await update.message.reply_text(
                text=message_text,
                parse_mode='Markdown'
            )

        except Exception as e:
            logger.error(f"خطأ في عرض إحصائيات النموذج: {e}")
            await update.message.reply_text("❌ خطأ في عرض الإحصائيات")

    async def test_model_bottom(self, update: Update, context: ContextTypes.DEFAULT_TYPE, model_key: str):
        """اختبار النموذج"""
        try:
            model_info = self.available_models[model_key]
            model_config = self.models_config.get(model_key, {})

            if model_config.get("status", "active") != "active":
                await update.message.reply_text(f"❌ {model_info['name']} متوقف حالياً\nيجب تشغيله أولاً لإجراء الاختبار")
                return

            await update.message.reply_text(f"🧪 جاري اختبار {model_info['name']}...")

            # محاكاة الاختبار
            import asyncio
            await asyncio.sleep(2)

            # نتائج الاختبار المحاكاة
            test_result = f"""✅ **نتائج اختبار {model_info['name']}**

🧪 **الاختبار البسيط:**
• الحالة: ✅ نجح
• وقت الاستجابة: 1.8 ثانية
• النقاط: 92/100

📊 **التقييم:**
• السرعة: جيد
• الجودة: ممتاز
• الاستقرار: مستقر

💡 **التوصية:** النموذج يعمل بشكل طبيعي ومتاح للاستخدام"""

            await update.message.reply_text(
                text=test_result,
                parse_mode='Markdown'
            )

        except Exception as e:
            logger.error(f"خطأ في اختبار النموذج: {e}")
            await update.message.reply_text("❌ خطأ في اختبار النموذج")
    
    async def show_model_details(self, update: Update, context: ContextTypes.DEFAULT_TYPE, model_key: str):
        """عرض تفاصيل نموذج محدد مع تحكم مباشر في الحالة"""
        try:
            if model_key not in self.available_models:
                await update.callback_query.answer("❌ نموذج غير موجود")
                return

            model_info = self.available_models[model_key]
            model_config = self.models_config.get(model_key, {})

            # حالة النموذج
            status = model_config.get("status", "active")
            status_text = "🟢 نشط ومتاح للاستخدام" if status == "active" else "🔴 متوقف وغير متاح"
            status_icon = "🟢" if status == "active" else "🔴"

            # إنشاء الأزرار - التحكم المباشر في الحالة
            keyboard = []

            # أزرار تغيير الحالة بشكل مباشر
            keyboard.append([
                InlineKeyboardButton(
                    f"{'🔴 إيقاف النموذج' if status == 'active' else '🟢 تشغيل النموذج'}",
                    callback_data=f"toggle_status_{model_key}"
                )
            ])

            # أزرار إضافية للتحكم
            keyboard.append([
                InlineKeyboardButton("🔄 إعادة تشغيل", callback_data=f"model_restart_{model_key}"),
                InlineKeyboardButton("⚙️ الإعدادات", callback_data=f"model_config_{model_key}")
            ])

            # أزرار المراقبة والاختبار
            keyboard.append([
                InlineKeyboardButton("📊 الإحصائيات", callback_data=f"model_stats_{model_key}"),
                InlineKeyboardButton("🧪 اختبار", callback_data=f"model_test_{model_key}")
            ])

            keyboard.append([InlineKeyboardButton("🔙 العودة للنماذج", callback_data="models_menu")])

            reply_markup = InlineKeyboardMarkup(keyboard)

            # تحديد نوع النموذج
            model_type_text = "رئيسي" if model_info['type'] == 'primary' else "فرعي (OpenRouter)"

            message_text = f"""🤖 **{model_info['name']}**

{status_icon} **الحالة الحالية:** {status_text}

📋 **معلومات النموذج:**
🏢 **المزود:** {model_info['provider']}
🏷️ **النوع:** {model_type_text}
🆔 **معرف النموذج:** `{model_info['model_id']}`
📝 **الوصف:** {model_info['description']}

⚙️ **الإعدادات النشطة:**
🎯 **الأولوية:** {model_config.get('priority', 1)} (كلما قل الرقم، زادت الأولوية)
🎫 **أقصى توكنز:** {model_config.get('max_tokens', 1000)} توكن
🌡️ **درجة الحرارة:** {model_config.get('temperature', 0.7)} (الإبداعية)
⏰ **المهلة الزمنية:** {model_config.get('timeout', 30)} ثانية
🛡️ **النظام الاحتياطي:** {'✅ مفعل' if model_config.get('fallback_enabled', True) else '❌ معطل'}

📅 **آخر تحديث:** {model_config.get('last_updated', 'غير محدد')[:16]}

💡 **استخدم الأزرار أدناه للتحكم في النموذج مباشرة:**"""

            await update.callback_query.edit_message_text(
                text=message_text,
                reply_markup=reply_markup,
                parse_mode='Markdown'
            )

        except Exception as e:
            logger.error(f"خطأ في عرض تفاصيل النموذج: {e}")
            await update.callback_query.answer("❌ خطأ في عرض تفاصيل النموذج")

    async def toggle_model_status_direct(self, update: Update, context: ContextTypes.DEFAULT_TYPE, model_key: str):
        """تبديل حالة النموذج مباشرة (تشغيل/إيقاف)"""
        try:
            if model_key not in self.available_models:
                await update.callback_query.answer("❌ نموذج غير موجود")
                return

            model_info = self.available_models[model_key]
            current_status = self.models_config.get(model_key, {}).get("status", "active")

            # تبديل الحالة
            new_status = "inactive" if current_status == "active" else "active"

            # تحديث الإعدادات
            if model_key not in self.models_config:
                self.models_config[model_key] = {}

            self.models_config[model_key]["status"] = new_status
            self.models_config[model_key]["last_updated"] = datetime.now().isoformat()

            # حفظ التغييرات
            self.save_models_config(self.models_config)

            # رسالة التأكيد
            if new_status == "active":
                confirmation_msg = f"✅ تم تشغيل {model_info['name']} بنجاح!"
                status_msg = "🟢 النموذج نشط الآن ومتاح للاستخدام في البوت"
            else:
                confirmation_msg = f"⏸️ تم إيقاف {model_info['name']} بنجاح!"
                status_msg = "🔴 النموذج متوقف الآن وغير متاح للاستخدام"

            await update.callback_query.answer(confirmation_msg)

            # إعادة عرض تفاصيل النموذج مع الحالة الجديدة
            await self.show_model_details(update, context, model_key)

            logger.info(f"🔄 تم تغيير حالة النموذج {model_key} إلى {new_status}")

        except Exception as e:
            logger.error(f"خطأ في تبديل حالة النموذج: {e}")
            await update.callback_query.answer("❌ خطأ في تغيير حالة النموذج")

    async def toggle_model_status(self, update: Update, context: ContextTypes.DEFAULT_TYPE, model_key: str, action: str):
        """تغيير حالة النموذج (تشغيل/إيقاف/إعادة تشغيل)"""
        try:
            if model_key not in self.available_models:
                await update.callback_query.answer("❌ نموذج غير موجود")
                return

            model_info = self.available_models[model_key]

            # تحديث الحالة
            if action == "start":
                self.models_config[model_key]["status"] = "active"
                status_text = "تم تشغيل"
                icon = "▶️"
            elif action == "stop":
                self.models_config[model_key]["status"] = "inactive"
                status_text = "تم إيقاف"
                icon = "⏸️"
            elif action == "restart":
                self.models_config[model_key]["status"] = "active"
                status_text = "تم إعادة تشغيل"
                icon = "🔄"

            # تحديث وقت التعديل
            self.models_config[model_key]["last_updated"] = datetime.now().isoformat()

            # حفظ التغييرات
            self.save_models_config(self.models_config)

            # إشعار المستخدم
            await update.callback_query.answer(f"{icon} {status_text} {model_info['name']}")

            # إعادة عرض تفاصيل النموذج
            await self.show_model_details(update, context, model_key)

        except Exception as e:
            logger.error(f"خطأ في تغيير حالة النموذج: {e}")
            await update.callback_query.answer("❌ خطأ في تغيير حالة النموذج")

    async def show_model_config(self, update: Update, context: ContextTypes.DEFAULT_TYPE, model_key: str):
        """عرض إعدادات النموذج"""
        try:
            if model_key not in self.available_models:
                await update.callback_query.answer("❌ نموذج غير موجود")
                return

            model_info = self.available_models[model_key]
            model_config = self.models_config.get(model_key, {})

            # إنشاء الأزرار
            keyboard = []

            # إعدادات الأولوية
            keyboard.append([
                InlineKeyboardButton("🔺 زيادة الأولوية", callback_data=f"config_priority_up_{model_key}"),
                InlineKeyboardButton("🔻 تقليل الأولوية", callback_data=f"config_priority_down_{model_key}")
            ])

            # إعدادات التوكنز
            keyboard.append([
                InlineKeyboardButton("📈 زيادة التوكنز", callback_data=f"config_tokens_up_{model_key}"),
                InlineKeyboardButton("📉 تقليل التوكنز", callback_data=f"config_tokens_down_{model_key}")
            ])

            # إعدادات درجة الحرارة
            keyboard.append([
                InlineKeyboardButton("🌡️⬆️ زيادة الحرارة", callback_data=f"config_temp_up_{model_key}"),
                InlineKeyboardButton("🌡️⬇️ تقليل الحرارة", callback_data=f"config_temp_down_{model_key}")
            ])

            # إعدادات المهلة الزمنية
            keyboard.append([
                InlineKeyboardButton("⏰⬆️ زيادة المهلة", callback_data=f"config_timeout_up_{model_key}"),
                InlineKeyboardButton("⏰⬇️ تقليل المهلة", callback_data=f"config_timeout_down_{model_key}")
            ])

            # تبديل النظام الاحتياطي
            fallback_text = "❌ تعطيل الاحتياطي" if model_config.get('fallback_enabled', True) else "✅ تفعيل الاحتياطي"
            keyboard.append([InlineKeyboardButton(fallback_text, callback_data=f"config_fallback_toggle_{model_key}")])

            # إعادة تعيين الإعدادات
            keyboard.append([InlineKeyboardButton("🔄 إعادة تعيين", callback_data=f"config_reset_{model_key}")])

            keyboard.append([InlineKeyboardButton("🔙 العودة", callback_data=f"model_{model_key}")])

            reply_markup = InlineKeyboardMarkup(keyboard)

            message_text = f"""⚙️ **إعدادات {model_info['name']}**

🎯 **الإعدادات الحالية:**

📊 **الأولوية:** {model_config.get('priority', 1)}
• كلما زادت الأولوية، كلما تم اختيار النموذج أولاً

🎫 **أقصى توكنز:** {model_config.get('max_tokens', 1000)}
• عدد الكلمات/الرموز المسموح بها في الرد

🌡️ **درجة الحرارة:** {model_config.get('temperature', 0.7)}
• تحكم في إبداعية الردود (0.1 = دقيق، 1.0 = إبداعي)

⏰ **المهلة الزمنية:** {model_config.get('timeout', 30)} ثانية
• الوقت الأقصى لانتظار الرد

🛡️ **النظام الاحتياطي:** {'مفعل ✅' if model_config.get('fallback_enabled', True) else 'معطل ❌'}
• التحويل لنموذج آخر عند الفشل

استخدم الأزرار أدناه لتعديل الإعدادات:"""

            await update.callback_query.edit_message_text(
                text=message_text,
                reply_markup=reply_markup,
                parse_mode='Markdown'
            )

        except Exception as e:
            logger.error(f"خطأ في عرض إعدادات النموذج: {e}")
            await update.callback_query.answer("❌ خطأ في عرض إعدادات النموذج")

    async def update_model_config(self, update: Update, context: ContextTypes.DEFAULT_TYPE, model_key: str, config_type: str, action: str):
        """تحديث إعدادات النموذج"""
        try:
            if model_key not in self.available_models:
                await update.callback_query.answer("❌ نموذج غير موجود")
                return

            model_config = self.models_config.get(model_key, {})

            # تحديث الإعدادات حسب النوع
            if config_type == "priority":
                current = model_config.get('priority', 1)
                if action == "up" and current < 10:
                    model_config['priority'] = current + 1
                elif action == "down" and current > 1:
                    model_config['priority'] = current - 1

            elif config_type == "tokens":
                current = model_config.get('max_tokens', 1000)
                if action == "up" and current < 4000:
                    model_config['max_tokens'] = current + 500
                elif action == "down" and current > 500:
                    model_config['max_tokens'] = current - 500

            elif config_type == "temp":
                current = model_config.get('temperature', 0.7)
                if action == "up" and current < 1.0:
                    model_config['temperature'] = round(current + 0.1, 1)
                elif action == "down" and current > 0.1:
                    model_config['temperature'] = round(current - 0.1, 1)

            elif config_type == "timeout":
                current = model_config.get('timeout', 30)
                if action == "up" and current < 120:
                    model_config['timeout'] = current + 10
                elif action == "down" and current > 10:
                    model_config['timeout'] = current - 10

            elif config_type == "fallback" and action == "toggle":
                current = model_config.get('fallback_enabled', True)
                model_config['fallback_enabled'] = not current

            elif config_type == "reset":
                # إعادة تعيين للقيم الافتراضية
                model_config.update({
                    "priority": 1,
                    "max_tokens": 1000,
                    "temperature": 0.7,
                    "timeout": 30,
                    "fallback_enabled": True
                })

            # تحديث وقت التعديل
            model_config['last_updated'] = datetime.now().isoformat()
            self.models_config[model_key] = model_config

            # حفظ التغييرات
            self.save_models_config(self.models_config)

            # إشعار المستخدم
            await update.callback_query.answer("✅ تم تحديث الإعدادات")

            # إعادة عرض إعدادات النموذج
            await self.show_model_config(update, context, model_key)

        except Exception as e:
            logger.error(f"خطأ في تحديث إعدادات النموذج: {e}")
            await update.callback_query.answer("❌ خطأ في تحديث الإعدادات")

    async def show_models_statistics(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """عرض إحصائيات جميع النماذج"""
        try:
            # جمع الإحصائيات
            active_models = 0
            inactive_models = 0
            total_models = len(self.available_models)

            models_by_type = {"normal": 0, "pro": 0, "reasoning": 0}
            models_by_provider = {}

            for model_key, model_info in self.available_models.items():
                model_config = self.models_config.get(model_key, {})

                # حساب النماذج النشطة/المتوقفة
                if model_config.get("status", "active") == "active":
                    active_models += 1
                else:
                    inactive_models += 1

                # حساب النماذج حسب النوع
                model_type = model_info.get("type", "normal")
                if model_type in models_by_type:
                    models_by_type[model_type] += 1

                # حساب النماذج حسب المزود
                provider = model_info.get("provider", "Unknown")
                models_by_provider[provider] = models_by_provider.get(provider, 0) + 1

            # إنشاء الأزرار
            keyboard = []

            # أزرار تفصيلية لكل نوع
            keyboard.append([
                InlineKeyboardButton("📊 النماذج العادية", callback_data="stats_normal"),
                InlineKeyboardButton("🧠 النماذج المتقدمة", callback_data="stats_pro")
            ])

            keyboard.append([
                InlineKeyboardButton("🔄 تحديث الإحصائيات", callback_data="models_stats"),
                InlineKeyboardButton("📈 تقرير مفصل", callback_data="models_detailed_report")
            ])

            keyboard.append([InlineKeyboardButton("🔙 العودة", callback_data="models_menu")])

            reply_markup = InlineKeyboardMarkup(keyboard)

            # إنشاء نص الإحصائيات
            providers_text = "\n".join([f"• {provider}: {count}" for provider, count in models_by_provider.items()])

            message_text = f"""📊 **إحصائيات النماذج الذكية**

🎯 **الإحصائيات العامة:**
• إجمالي النماذج: {total_models}
• النماذج النشطة: {active_models} 🟢
• النماذج المتوقفة: {inactive_models} 🔴

📈 **التوزيع حسب النوع:**
• النماذج العادية: {models_by_type['normal']}
• النماذج المتقدمة: {models_by_type['pro']}
• نماذج التفكير: {models_by_type['reasoning']}

🏢 **التوزيع حسب المزود:**
{providers_text}

📅 **آخر تحديث:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

اختر خياراً لمزيد من التفاصيل:"""

            await update.callback_query.edit_message_text(
                text=message_text,
                reply_markup=reply_markup,
                parse_mode='Markdown'
            )

        except Exception as e:
            logger.error(f"خطأ في عرض إحصائيات النماذج: {e}")
            await update.callback_query.answer("❌ خطأ في عرض الإحصائيات")

    async def test_model(self, update: Update, context: ContextTypes.DEFAULT_TYPE, model_key: str):
        """اختبار النموذج"""
        try:
            if model_key not in self.available_models:
                await update.callback_query.answer("❌ نموذج غير موجود")
                return

            model_info = self.available_models[model_key]
            model_config = self.models_config.get(model_key, {})

            # التحقق من حالة النموذج
            if model_config.get("status", "active") != "active":
                await update.callback_query.answer("❌ النموذج متوقف حالياً")
                return

            # إنشاء الأزرار
            keyboard = []

            keyboard.append([
                InlineKeyboardButton("🧪 اختبار بسيط", callback_data=f"test_simple_{model_key}"),
                InlineKeyboardButton("🔬 اختبار متقدم", callback_data=f"test_advanced_{model_key}")
            ])

            keyboard.append([
                InlineKeyboardButton("⚡ اختبار السرعة", callback_data=f"test_speed_{model_key}"),
                InlineKeyboardButton("🎯 اختبار الدقة", callback_data=f"test_accuracy_{model_key}")
            ])

            keyboard.append([InlineKeyboardButton("🔙 العودة", callback_data=f"model_{model_key}")])

            reply_markup = InlineKeyboardMarkup(keyboard)

            message_text = f"""🧪 **اختبار {model_info['name']}**

🎯 **معلومات الاختبار:**
• النموذج: {model_info['name']}
• المزود: {model_info['provider']}
• الحالة: {'🟢 نشط' if model_config.get('status') == 'active' else '🔴 متوقف'}

🔬 **أنواع الاختبارات المتاحة:**

**🧪 اختبار بسيط:**
• سؤال بسيط للتحقق من الاستجابة

**🔬 اختبار متقدم:**
• أسئلة معقدة لاختبار القدرات

**⚡ اختبار السرعة:**
• قياس زمن الاستجابة

**🎯 اختبار الدقة:**
• تقييم جودة الإجابات

اختر نوع الاختبار:"""

            await update.callback_query.edit_message_text(
                text=message_text,
                reply_markup=reply_markup,
                parse_mode='Markdown'
            )

        except Exception as e:
            logger.error(f"خطأ في اختبار النموذج: {e}")
            await update.callback_query.answer("❌ خطأ في اختبار النموذج")

    async def run_model_test(self, update: Update, context: ContextTypes.DEFAULT_TYPE, model_key: str, test_type: str):
        """تشغيل اختبار النموذج"""
        try:
            model_info = self.available_models[model_key]

            # رسائل الاختبار
            test_messages = {
                "simple": "مرحبا، كيف حالك؟",
                "advanced": "اشرح لي مفهوم الذكاء الاصطناعي وتطبيقاته في التصميم الجرافيكي",
                "speed": "ما هو 2+2؟",
                "accuracy": "اكتب قائمة بـ 5 نصائح لتصميم شعار احترافي"
            }

            test_message = test_messages.get(test_type, "اختبار عام")

            # إشعار بدء الاختبار
            await update.callback_query.answer("🧪 جاري تشغيل الاختبار...")

            # محاكاة الاختبار (في التطبيق الحقيقي، ستتصل بالنموذج الفعلي)
            import time
            start_time = time.time()

            # محاكاة وقت الاستجابة
            await context.application.create_task(self._simulate_test_delay(test_type))

            end_time = time.time()
            response_time = round(end_time - start_time, 2)

            # نتائج الاختبار المحاكاة
            test_results = {
                "simple": {
                    "success": True,
                    "response": "مرحبا! أنا بخير، شكراً لسؤالك. كيف يمكنني مساعدتك؟",
                    "score": 95
                },
                "advanced": {
                    "success": True,
                    "response": "الذكاء الاصطناعي في التصميم يشمل: 1) إنشاء تصاميم تلقائية...",
                    "score": 88
                },
                "speed": {
                    "success": True,
                    "response": "4",
                    "score": 100
                },
                "accuracy": {
                    "success": True,
                    "response": "1. البساطة والوضوح\n2. الألوان المناسبة\n3. الخط المقروء...",
                    "score": 92
                }
            }

            result = test_results.get(test_type, {"success": False, "response": "خطأ", "score": 0})

            # إنشاء تقرير الاختبار
            status_icon = "✅" if result["success"] else "❌"
            score_icon = "🟢" if result["score"] >= 80 else "🟡" if result["score"] >= 60 else "🔴"

            keyboard = [[InlineKeyboardButton("🔙 العودة", callback_data=f"test_{model_key}")]]
            reply_markup = InlineKeyboardMarkup(keyboard)

            message_text = f"""📋 **تقرير اختبار {model_info['name']}**

🧪 **نوع الاختبار:** {test_type.title()}
{status_icon} **النتيجة:** {'نجح' if result['success'] else 'فشل'}
⏱️ **زمن الاستجابة:** {response_time} ثانية
{score_icon} **النقاط:** {result['score']}/100

📝 **السؤال:**
{test_message}

💬 **الإجابة:**
{result['response'][:200]}{'...' if len(result['response']) > 200 else ''}

📊 **التقييم:**
• السرعة: {'ممتاز' if response_time < 2 else 'جيد' if response_time < 5 else 'بطيء'}
• الجودة: {'ممتاز' if result['score'] >= 90 else 'جيد' if result['score'] >= 70 else 'مقبول'}
• الاستقرار: {'مستقر' if result['success'] else 'غير مستقر'}"""

            await update.callback_query.edit_message_text(
                text=message_text,
                reply_markup=reply_markup,
                parse_mode='Markdown'
            )

        except Exception as e:
            logger.error(f"خطأ في تشغيل اختبار النموذج: {e}")
            await update.callback_query.answer("❌ خطأ في تشغيل الاختبار")

    async def _simulate_test_delay(self, test_type: str):
        """محاكاة تأخير الاختبار"""
        import asyncio
        delays = {
            "simple": 1,
            "advanced": 3,
            "speed": 0.5,
            "accuracy": 2
        }
        await asyncio.sleep(delays.get(test_type, 1))
