# 🤖 نظام صلاح الدين الدروبي المتقدم - الإصدار الاحترافي

## 🎯 نظرة عامة
نظام بوت تلجرام متكامل واحترافي يتكون من:
- 📱 **البوت الرئيسي**: للمستخدمين العاديين مع نظام المحافظ وإكسا الذكي
- 🛡️ **بوت الإدارة والمراقبة الموحد**: للإدارة والمراقبة المتقدمة مع نظام المحافظ

## 🚀 التشغيل السريع

### ملف تشغيل موحد واحد (الطريقة الاحترافية)
```bash
python start_system.py
```

### تشغيل البوتات منفصلة (للتطوير)
```bash
# البوت الرئيسي
python main/main.py

# بوت الإدارة والمراقبة
python admin/admin.py
```

## الميزات الرئيسية

### 🤖 البوت الرئيسي
- معلومات شخصية ومهنية شاملة
- مساعد ذكي متطور (إكسا الذكي العادي وإكسا الذكي برو)
- نظام المحافظ التلقائي مع عملة إكسا
- دعم كامل للملفات والصور
- واجهة بسيطة وسهلة الاستخدام

### 🛡️ بوت الإدارة والمراقبة
- مراقبة شاملة للبوت الرئيسي
- إدارة نظام المحافظ والمعاملات
- إحصائيات مفصلة ومباشرة
- إدارة المستخدمين والنشرات
- تقارير وتحليلات متقدمة

## المتطلبات
- Python 3.8+
- python-telegram-bot==20.7
- openai==1.12.0
- httpx==0.25.2

## الإعداد

### 1. إعداد البوت الرئيسي
```python
# في main/core/config.py
BOT_TOKEN = "YOUR_MAIN_BOT_TOKEN"
DEEPSEEK_API_KEY = "YOUR_DEEPSEEK_API_KEY"
DEEPSEEK_PRO_API_KEY = "YOUR_DEEPSEEK_PRO_API_KEY"
```

### 2. إعداد بوت الإدارة
```python
# في admin/core/config.py
ADMIN_BOT_TOKEN = "YOUR_ADMIN_BOT_TOKEN"
ADMIN_USER_ID = "YOUR_ADMIN_USER_ID"
```

## هيكل المشروع
```
├── start_system.py         # مدير التشغيل الموحد (الملف الوحيد للتشغيل)
├── main/                   # البوت الرئيسي
│   ├── main.py
│   ├── core/
│   ├── features/
│   └── media/
├── admin/                  # بوت الإدارة والمراقبة
│   ├── admin.py
│   ├── core/
│   ├── management/
│   └── monitoring/
├── shared/                 # الملفات المشتركة
│   ├── database/
│   ├── data_processing/
│   ├── utils/
│   └── config/
└── docs/                   # التوثيق الشامل
```

## الاستخدام

### للمستخدمين العاديين
1. ابحث عن البوت الرئيسي في تلجرام
2. أرسل `/start`
3. استخدم الأزرار للتنقل
4. جرب المساعد الذكي "إكسا" (العادي والبرو)
5. استخدم نظام المحافظ لإدارة رصيدك

### للمدير
1. ابحث عن بوت الإدارة في تلجرام
2. أرسل `/start` أو اكتب "مدير"
3. استخدم الأزرار للإدارة والمراقبة
4. إدارة المحافظ والمعاملات

## الميزات المتقدمة

### 🏦 نظام المحافظ
- إنشاء محفظة تلقائي لكل مستخدم جديد
- عملة إكسا (1 إكسا = 3 USD)
- إدارة الرصيد والمعاملات
- نظام أمان متقدم للمحافظ

### 🤖 إكسا الذكي المتطور
- **إكسا العادي**: للمحادثات العامة والأسئلة البسيطة
- **إكسا برو**: للاستشارات المتقدمة والمعقدة
- دعم تاريخ المحادثة
- ردود ذكية ومخصصة

### 🔍 نظام المراقبة
- إشعارات فورية لجميع الأنشطة
- تمييز المستخدمين الجدد والقدامى
- إحصائيات مفصلة ومباشرة
- مراقبة الأداء والاستقرار

### 📁 إدارة الملفات
- دعم الصور والمستندات
- ملفات صوتية ومرئية
- رسائل صوتية
- تحليل تفصيلي للملفات

### 🔐 الأمان
- فصل كامل بين البوتين
- حماية البيانات والمحافظ
- معالجة أخطاء متقدمة
- مراقبة الأمان المستمرة

## الدعم والصيانة

### 🔄 التشغيل الموحد
- تشغيل البوتين معاً بملف واحد
- مراقبة صامتة في الخلفية
- إدارة الخيوط والعمليات

### 🛠️ الصيانة التلقائية
- مراقبة مستمرة للنظام
- تسجيل شامل في ملفات السجلات
- معالجة الأخطاء التلقائية

### ⚡ الإيقاف الآمن
استخدم `Ctrl+C` لإيقاف النظام بأمان مع رسالة تأكيد.

## الترخيص
هذا المشروع مخصص للاستخدام الشخصي لصلاح الدين الدروبي.

---
**🎯 نظام احترافي متكامل للبوتات مع مراقبة وإدارة متقدمة**
