Traceback (most recent call last):
  File "D:\Salah_Bot\simple_test.py", line 165, in <module>
    main()
  File "D:\Salah_Bot\simple_test.py", line 130, in main
    print("\U0001f680 ╚╧┴ ╟╬╩╚╟╤ ╘╟عط طغ┘╟ع ▌═╒ ═╟ط╔ ╟طغع╟╨╠")
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\encodings\cp1256.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>
